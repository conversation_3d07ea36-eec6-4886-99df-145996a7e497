#!/usr/bin/env python3
"""
Comprehensive Plugin System Demonstration

This script provides a complete demonstration of the enhanced plugin system,
showcasing all features including hot reload, function exposure, model integration,
and real-world usage scenarios.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agent_framework.core.config import FrameworkConfig, PluginConfig
from agent_framework.plugins import (
    PluginManager, HotReloadConfig, ModelIntegration,
    FunctionCallRequest
)


class PluginSystemDemo:
    """Comprehensive demonstration of the enhanced plugin system."""
    
    def __init__(self):
        """Initialize the demo."""
        self.logger = logging.getLogger(__name__)
        self.plugin_manager = None
        self.model_integration = None
        
        # Demo configuration
        self.config = self._create_config()
        self.hot_reload_config = self._create_hot_reload_config()
    
    def _create_config(self) -> FrameworkConfig:
        """Create framework configuration for demo."""
        config = FrameworkConfig()
        
        # Plugin configuration
        config.plugins.plugin_directories = ["plugins", "examples"]
        config.plugins.auto_load_plugins = True
        config.plugins.hot_reload_enabled = True
        config.plugins.function_exposure_enabled = True
        config.plugins.function_validation_enabled = True
        
        return config
    
    def _create_hot_reload_config(self) -> HotReloadConfig:
        """Create hot reload configuration for demo."""
        return HotReloadConfig(
            enabled=True,
            debounce_delay=1.0,
            max_reload_attempts=3,
            reload_timeout=30.0,
            log_reload_events=True
        )
    
    async def initialize(self) -> None:
        """Initialize the plugin system."""
        self.logger.info("🚀 Initializing Enhanced Plugin System Demo")
        
        # Create plugin manager
        self.plugin_manager = PluginManager(
            self.config, 
            hot_reload_config=self.hot_reload_config
        )
        
        # Initialize
        await self.plugin_manager.initialize()
        
        # Create model integration
        self.model_integration = ModelIntegration(self.plugin_manager)
        
        self.logger.info("✅ Plugin system initialized successfully")
    
    async def cleanup(self) -> None:
        """Clean up the plugin system."""
        if self.plugin_manager:
            await self.plugin_manager.shutdown()
        self.logger.info("🧹 Plugin system cleaned up")
    
    async def run_demo(self) -> None:
        """Run the complete demonstration."""
        try:
            await self.initialize()
            
            # Run all demo sections
            await self.demo_plugin_discovery()
            await self.demo_function_exposure()
            await self.demo_model_integration()
            await self.demo_function_calling()
            await self.demo_hot_reload()
            await self.demo_error_handling()
            await self.demo_performance_monitoring()
            await self.demo_ai_model_scenarios()
            
            self.logger.info("🎉 Demo completed successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Demo failed: {e}", exc_info=True)
            raise
        finally:
            await self.cleanup()
    
    async def demo_plugin_discovery(self) -> None:
        """Demonstrate plugin discovery and loading."""
        self.logger.info("\n" + "="*60)
        self.logger.info("📦 PLUGIN DISCOVERY AND LOADING")
        self.logger.info("="*60)
        
        # Get plugin status
        status = await self.plugin_manager.get_plugin_status()
        
        self.logger.info(f"📊 Plugin System Status:")
        self.logger.info(f"  • Total plugins discovered: {status['registry_stats']['total_plugins']}")
        self.logger.info(f"  • Plugins loaded: {status['loaded_count']}")
        self.logger.info(f"  • Hot reload active: {self.plugin_manager.is_hot_reload_active()}")
        
        # List loaded plugins
        all_plugins = self.plugin_manager.registry.get_all_plugins()
        if all_plugins:
            self.logger.info(f"\n📋 Loaded Plugins:")
            for plugin_name in all_plugins.keys():
                self.logger.info(f"  ✓ {plugin_name}")
        
        # Show plugin details
        all_plugins = self.plugin_manager.registry.get_all_plugins()
        self.logger.info(f"\n🔍 Plugin Details:")
        for name, metadata in all_plugins.items():
            self.logger.info(f"  • {name} v{metadata.version}")
            self.logger.info(f"    Description: {metadata.description}")
            self.logger.info(f"    Author: {metadata.author}")
            if metadata.dependencies:
                self.logger.info(f"    Dependencies: {', '.join(metadata.dependencies)}")
    
    async def demo_function_exposure(self) -> None:
        """Demonstrate function exposure and discovery."""
        self.logger.info("\n" + "="*60)
        self.logger.info("🔧 FUNCTION EXPOSURE AND DISCOVERY")
        self.logger.info("="*60)
        
        # Get all available functions
        functions = self.plugin_manager.get_available_functions()
        
        self.logger.info(f"🎯 Available Functions: {len(functions)}")
        
        # Group functions by plugin
        functions_by_plugin = {}
        for func_name, func_info in functions.items():
            plugin_name = func_info['plugin_name']
            if plugin_name not in functions_by_plugin:
                functions_by_plugin[plugin_name] = []
            functions_by_plugin[plugin_name].append(func_name)
        
        # Display functions by plugin
        for plugin_name, func_names in functions_by_plugin.items():
            self.logger.info(f"\n📦 {plugin_name} ({len(func_names)} functions):")
            for func_name in func_names:
                func_info = functions[func_name]
                self.logger.info(f"  • {func_name}")
                self.logger.info(f"    📝 {func_info['description']}")
                if func_info['tags']:
                    self.logger.info(f"    🏷️  Tags: {', '.join(func_info['tags'])}")
                
                # Show parameters
                if func_info['parameters']:
                    param_summary = []
                    for param in func_info['parameters']:
                        param_str = f"{param['name']}:{param['type']}"
                        if not param['required']:
                            param_str += "?"
                        param_summary.append(param_str)
                    self.logger.info(f"    📋 Parameters: {', '.join(param_summary)}")
    
    async def demo_model_integration(self) -> None:
        """Demonstrate AI model integration features."""
        self.logger.info("\n" + "="*60)
        self.logger.info("🤖 AI MODEL INTEGRATION")
        self.logger.info("="*60)
        
        # Get model-formatted functions
        model_functions = self.model_integration.get_available_functions()
        self.logger.info(f"🎯 Functions available to AI models: {len(model_functions)}")
        
        # Show different schema formats
        self.logger.info(f"\n📋 Schema Formats:")
        
        # OpenAI format
        openai_schemas = self.model_integration.get_functions_schema(format="openai")
        self.logger.info(f"  • OpenAI format: {len(openai_schemas)} schemas")
        
        # Anthropic format
        anthropic_schemas = self.model_integration.get_functions_schema(format="anthropic")
        self.logger.info(f"  • Anthropic format: {len(anthropic_schemas)} schemas")
        
        # Show example schema
        if openai_schemas:
            example_schema = openai_schemas[0]
            self.logger.info(f"\n📄 Example OpenAI Schema:")
            self.logger.info(f"  Function: {example_schema['name']}")
            self.logger.info(f"  Description: {example_schema['description']}")
            
            if 'parameters' in example_schema and 'properties' in example_schema['parameters']:
                props = example_schema['parameters']['properties']
                self.logger.info(f"  Parameters: {list(props.keys())}")
        
        # Function categories
        categories = self.model_integration.get_function_categories()
        if categories:
            self.logger.info(f"\n🗂️  Function Categories:")
            for category, func_names in categories.items():
                self.logger.info(f"  • {category}: {len(func_names)} functions")
    
    async def demo_function_calling(self) -> None:
        """Demonstrate function calling capabilities."""
        self.logger.info("\n" + "="*60)
        self.logger.info("📞 FUNCTION CALLING DEMONSTRATION")
        self.logger.info("="*60)
        
        # Find available functions to test
        functions = self.model_integration.get_available_functions()
        
        # Test different types of functions
        test_cases = [
            {
                "pattern": "add",
                "args": {"a": 15, "b": 27},
                "description": "Mathematical addition"
            },
            {
                "pattern": "process_text",
                "args": {"text": "Hello World", "operation": "uppercase"},
                "description": "Text processing"
            },
            {
                "pattern": "calculate",
                "args": {"a": 8, "b": 3, "operation": "multiply"},
                "description": "Calculator operation"
            },
            {
                "pattern": "greet",
                "args": {"name": "Plugin System"},
                "description": "Greeting function"
            }
        ]
        
        successful_calls = 0
        total_calls = 0
        
        for test_case in test_cases:
            # Find matching function
            matching_functions = [
                name for name in functions.keys() 
                if test_case["pattern"] in name.lower()
            ]
            
            if matching_functions:
                func_name = matching_functions[0]
                self.logger.info(f"\n🧪 Testing: {test_case['description']}")
                self.logger.info(f"   Function: {func_name}")
                self.logger.info(f"   Arguments: {test_case['args']}")
                
                try:
                    request = FunctionCallRequest(
                        function_name=func_name,
                        arguments=test_case['args']
                    )
                    
                    start_time = time.time()
                    result = await self.model_integration.call_function(request)
                    execution_time = time.time() - start_time
                    
                    total_calls += 1
                    
                    if result.success:
                        successful_calls += 1
                        self.logger.info(f"   ✅ Result: {result.result}")
                        self.logger.info(f"   ⏱️  Execution time: {execution_time*1000:.2f}ms")
                    else:
                        self.logger.error(f"   ❌ Error: {result.error}")
                
                except Exception as e:
                    total_calls += 1
                    self.logger.error(f"   💥 Exception: {e}")
        
        # Summary
        success_rate = (successful_calls / total_calls * 100) if total_calls > 0 else 0
        self.logger.info(f"\n📊 Function Calling Summary:")
        self.logger.info(f"   • Total calls: {total_calls}")
        self.logger.info(f"   • Successful: {successful_calls}")
        self.logger.info(f"   • Success rate: {success_rate:.1f}%")
    
    async def demo_hot_reload(self) -> None:
        """Demonstrate hot reload capabilities."""
        self.logger.info("\n" + "="*60)
        self.logger.info("🔥 HOT RELOAD DEMONSTRATION")
        self.logger.info("="*60)
        
        if not self.plugin_manager.is_hot_reload_active():
            self.logger.warning("⚠️  Hot reload is not active")
            return
        
        # Get reload history
        history = self.plugin_manager.get_reload_history()
        self.logger.info(f"📜 Reload History: {len(history)} events")
        
        if history:
            self.logger.info("   Recent reload events:")
            for event in history[-5:]:  # Show last 5 events
                status = "✅" if event.success else "❌"
                self.logger.info(f"   {status} {event.plugin_name} - {event.event_type}")
        
        # Test manual reload if plugins are available
        all_plugins = self.plugin_manager.registry.get_all_plugins()
        if all_plugins:
            plugin_name = list(all_plugins.keys())[0]
            
            self.logger.info(f"\n🔄 Testing manual reload of '{plugin_name}'...")
            
            try:
                reload_event = await self.plugin_manager.reload_plugin(plugin_name, force=True)
                
                if reload_event.success:
                    self.logger.info(f"   ✅ Reload successful in {reload_event.reload_time:.3f}s")
                else:
                    self.logger.error(f"   ❌ Reload failed: {reload_event.error}")
            
            except Exception as e:
                self.logger.error(f"   💥 Reload exception: {e}")
    
    async def demo_error_handling(self) -> None:
        """Demonstrate error handling capabilities."""
        self.logger.info("\n" + "="*60)
        self.logger.info("🛡️  ERROR HANDLING DEMONSTRATION")
        self.logger.info("="*60)
        
        error_test_cases = [
            {
                "name": "Non-existent function",
                "function": "non_existent_function",
                "args": {}
            },
            {
                "name": "Invalid arguments",
                "function": None,  # Will be set to a real function
                "args": {"invalid_param": "value"}
            },
            {
                "name": "Division by zero",
                "function": None,  # Will be set to divide function
                "args": {"a": 10, "b": 0}
            }
        ]
        
        # Find real functions for some test cases
        functions = self.model_integration.get_available_functions()
        
        # Find a function for invalid arguments test
        if functions:
            error_test_cases[1]["function"] = list(functions.keys())[0]
        
        # Find divide function for division by zero test
        divide_functions = [name for name in functions.keys() if "divide" in name.lower()]
        if divide_functions:
            error_test_cases[2]["function"] = divide_functions[0]
        
        # Run error tests
        for test_case in error_test_cases:
            if test_case["function"] is None:
                continue
            
            self.logger.info(f"\n🧪 Testing: {test_case['name']}")
            
            try:
                request = FunctionCallRequest(
                    function_name=test_case["function"],
                    arguments=test_case["args"]
                )
                
                result = await self.model_integration.call_function(request)
                
                if result.success:
                    self.logger.info(f"   ⚠️  Unexpected success: {result.result}")
                else:
                    self.logger.info(f"   ✅ Expected error handled: {result.error}")
            
            except Exception as e:
                self.logger.info(f"   ✅ Exception handled: {e}")
    
    async def demo_performance_monitoring(self) -> None:
        """Demonstrate performance monitoring capabilities."""
        self.logger.info("\n" + "="*60)
        self.logger.info("📈 PERFORMANCE MONITORING")
        self.logger.info("="*60)
        
        # Get call statistics
        stats = self.model_integration.get_call_statistics()
        
        self.logger.info(f"📊 Function Call Statistics:")
        self.logger.info(f"   • Total calls: {stats['total_calls']}")
        self.logger.info(f"   • Average execution time: {stats['average_execution_time']*1000:.2f}ms")
        
        if stats['most_called']:
            self.logger.info(f"\n🏆 Most Called Functions:")
            for func_name, func_stats in stats['most_called'][:5]:
                self.logger.info(f"   • {func_name}: {func_stats['call_count']} calls")
        
        # Plugin system performance
        plugin_status = await self.plugin_manager.get_plugin_status()
        self.logger.info(f"\n⚡ System Performance:")
        self.logger.info(f"   • Plugins loaded: {plugin_status['loaded_count']}")
        self.logger.info(f"   • Functions available: {len(self.plugin_manager.get_available_functions())}")
        self.logger.info(f"   • Hot reload active: {self.plugin_manager.is_hot_reload_active()}")
    
    async def demo_ai_model_scenarios(self) -> None:
        """Demonstrate real-world AI model integration scenarios."""
        self.logger.info("\n" + "="*60)
        self.logger.info("🤖 AI MODEL INTEGRATION SCENARIOS")
        self.logger.info("="*60)
        
        # Scenario 1: Function Discovery
        self.logger.info("\n🔍 Scenario 1: AI Model Function Discovery")
        search_results = self.model_integration.search_functions("calculate", max_results=3)
        self.logger.info(f"   AI searches for 'calculate' functions:")
        for result in search_results:
            self.logger.info(f"   • {result['name']} (relevance: {result['relevance_score']:.2f})")
            self.logger.info(f"     {result['description']}")
        
        # Scenario 2: Multi-step workflow
        self.logger.info("\n🔄 Scenario 2: Multi-step AI Workflow")
        
        # Step 1: Find text processing function
        text_functions = self.model_integration.search_functions("text", max_results=1)
        if text_functions:
            func_name = text_functions[0]['name']
            self.logger.info(f"   Step 1: AI finds text function: {func_name}")
            
            # Step 2: Process text
            request = FunctionCallRequest(
                function_name=func_name,
                arguments={"text": "hello world", "operation": "uppercase"}
            )
            result = await self.model_integration.call_function(request)
            
            if result.success:
                processed_text = result.result
                self.logger.info(f"   Step 2: Text processed: '{processed_text}'")
                
                # Step 3: Use result in another function (if available)
                calc_functions = self.model_integration.search_functions("calculate", max_results=1)
                if calc_functions:
                    self.logger.info(f"   Step 3: AI chains to calculation function")
                    # This demonstrates how AI can chain function calls
        
        # Scenario 3: Error recovery
        self.logger.info("\n🛡️  Scenario 3: AI Error Recovery")
        
        # Try invalid function first
        request = FunctionCallRequest(
            function_name="invalid_function",
            arguments={}
        )
        result = await self.model_integration.call_function(request)
        
        if not result.success:
            self.logger.info(f"   AI encounters error: {result.error}")
            
            # AI recovers by finding valid function
            functions = self.model_integration.get_available_functions()
            if functions:
                valid_func = list(functions.keys())[0]
                self.logger.info(f"   AI recovers with valid function: {valid_func}")
        
        self.logger.info("\n✨ AI Model Integration scenarios completed!")


async def main():
    """Main demo function."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Create and run demo
    demo = PluginSystemDemo()
    await demo.run_demo()


if __name__ == "__main__":
    print("🚀 Enhanced Plugin System - Comprehensive Demonstration")
    print("=" * 60)
    print("This demo showcases all features of the enhanced plugin system:")
    print("• Plugin discovery and loading")
    print("• Function exposure and registration")
    print("• AI model integration")
    print("• Hot reload capabilities")
    print("• Error handling")
    print("• Performance monitoring")
    print("• Real-world AI scenarios")
    print("=" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo failed: {e}")
        sys.exit(1)
