#!/usr/bin/env python3
"""
Test script for the enhanced plugin system.

This script demonstrates and tests the new hot reload, function exposure,
and model integration capabilities of the enhanced plugin system.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agent_framework.core.config import FrameworkConfig, PluginConfig
from agent_framework.plugins import (
    PluginManager, HotReloadConfig, ModelIntegration,
    FunctionCallRequest
)


async def test_enhanced_plugin_system():
    """Test the enhanced plugin system capabilities."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    logger.info("Starting enhanced plugin system test")
    
    try:
        # Create configuration
        config = FrameworkConfig()
        config.plugins.plugin_directories = ["plugins"]
        config.plugins.hot_reload_enabled = True
        config.plugins.function_exposure_enabled = True
        
        # Create hot reload configuration
        hot_reload_config = HotReloadConfig(
            enabled=True,
            debounce_delay=0.5,  # Faster for testing
            max_reload_attempts=3,
            log_reload_events=True
        )
        
        # Initialize plugin manager
        plugin_manager = PluginManager(config, hot_reload_config=hot_reload_config)
        await plugin_manager.initialize()
        
        logger.info("Plugin manager initialized")
        
        # Test 1: Plugin Discovery and Loading
        logger.info("\n=== Test 1: Plugin Discovery and Loading ===")

        stats = await plugin_manager.get_plugin_status()
        logger.info(f"Plugin stats - Total plugins: {stats['registry_stats']['total_plugins']}")
        logger.info(f"Loaded plugins: {stats['loaded_count']}")
        
        # Test 2: Function Discovery
        logger.info("\n=== Test 2: Function Discovery ===")
        
        available_functions = plugin_manager.get_available_functions()
        logger.info(f"Available functions: {len(available_functions)}")
        
        for func_name, func_info in available_functions.items():
            logger.info(f"  - {func_name}: {func_info['description']}")
        
        # Test 3: Model Integration
        logger.info("\n=== Test 3: Model Integration ===")
        
        model_integration = ModelIntegration(plugin_manager)
        
        # Get functions formatted for AI model
        model_functions = model_integration.get_available_functions()
        logger.info(f"Model functions: {len(model_functions)}")
        
        # Get OpenAI-style schemas
        openai_schemas = model_integration.get_functions_schema(format="openai")
        logger.info(f"OpenAI schemas: {len(openai_schemas)}")
        
        # Test 4: Function Calling
        logger.info("\n=== Test 4: Function Calling ===")
        
        if "example_enhanced.process_text" in available_functions:
            # Test text processing function
            request = FunctionCallRequest(
                function_name="example_enhanced.process_text",
                arguments={
                    "text": "Hello World",
                    "operation": "uppercase",
                    "strip_whitespace": True
                }
            )
            
            result = await model_integration.call_function(request)
            logger.info(f"Text processing result: {result}")
            
            # Test calculation function
            if "example_enhanced.calculate" in available_functions:
                calc_request = FunctionCallRequest(
                    function_name="example_enhanced.calculate",
                    arguments={
                        "a": 10.5,
                        "b": 3.2,
                        "operation": "multiply"
                    }
                )
                
                calc_result = await model_integration.call_function(calc_request)
                logger.info(f"Calculation result: {calc_result}")
            
            # Test cache management
            if "example_enhanced.manage_cache" in available_functions:
                # Set a value
                cache_set_request = FunctionCallRequest(
                    function_name="example_enhanced.manage_cache",
                    arguments={
                        "action": "set",
                        "key": "test_key",
                        "value": {"test": "data", "timestamp": time.time()}
                    }
                )
                
                cache_set_result = await model_integration.call_function(cache_set_request)
                logger.info(f"Cache set result: {cache_set_result}")
                
                # Get the value
                cache_get_request = FunctionCallRequest(
                    function_name="example_enhanced.manage_cache",
                    arguments={
                        "action": "get",
                        "key": "test_key"
                    }
                )
                
                cache_get_result = await model_integration.call_function(cache_get_request)
                logger.info(f"Cache get result: {cache_get_result}")
        
        # Test 5: Function Search
        logger.info("\n=== Test 5: Function Search ===")
        
        search_results = model_integration.search_functions("text", max_results=5)
        logger.info(f"Search results for 'text': {len(search_results)}")
        for result in search_results:
            logger.info(f"  - {result['name']}: {result['description']}")
        
        # Test 6: Statistics and Monitoring
        logger.info("\n=== Test 6: Statistics and Monitoring ===")
        
        call_stats = model_integration.get_call_statistics()
        logger.info(f"Call statistics: {json.dumps(call_stats, indent=2)}")
        
        # Test 7: Hot Reload (if enabled)
        logger.info("\n=== Test 7: Hot Reload Testing ===")
        
        if plugin_manager.is_hot_reload_active():
            logger.info("Hot reload is active")
            
            # Get reload history
            reload_history = plugin_manager.get_reload_history()
            logger.info(f"Reload history: {len(reload_history)} events")
            
            # Test manual reload
            if "example_enhanced" in [name for name, _ in plugin_manager.registry.get_all_plugins().items()]:
                logger.info("Testing manual plugin reload...")
                reload_event = await plugin_manager.reload_plugin("example_enhanced", force=True)
                logger.info(f"Reload event: {reload_event}")
                
                # Verify functions are still available after reload
                post_reload_functions = plugin_manager.get_available_functions()
                logger.info(f"Functions after reload: {len(post_reload_functions)}")
        else:
            logger.info("Hot reload is not active")
        
        # Test 8: Error Handling
        logger.info("\n=== Test 8: Error Handling ===")
        
        # Test calling non-existent function
        error_request = FunctionCallRequest(
            function_name="non_existent_function",
            arguments={}
        )
        
        error_result = await model_integration.call_function(error_request)
        logger.info(f"Error handling result: {error_result}")
        
        # Test invalid arguments
        if "example_enhanced.calculate" in available_functions:
            invalid_request = FunctionCallRequest(
                function_name="example_enhanced.calculate",
                arguments={
                    "a": "not_a_number",  # Invalid type
                    "b": 5,
                    "operation": "add"
                }
            )
            
            invalid_result = await model_integration.call_function(invalid_request)
            logger.info(f"Invalid arguments result: {invalid_result}")
        
        # Test 9: Function Categories and Organization
        logger.info("\n=== Test 9: Function Categories ===")
        
        categories = model_integration.get_function_categories()
        logger.info(f"Function categories: {json.dumps(categories, indent=2)}")
        
        logger.info("\n=== Enhanced Plugin System Test Complete ===")
        logger.info("All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        return False
    
    finally:
        # Cleanup
        try:
            await plugin_manager.shutdown()
            logger.info("Plugin manager shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    return True


async def demonstrate_ai_model_integration():
    """Demonstrate how an AI model would interact with the plugin system."""
    
    logger = logging.getLogger(__name__)
    logger.info("\n=== AI Model Integration Demonstration ===")
    
    # This simulates how an AI model would discover and use plugin functions
    
    try:
        # Initialize the system (same as above)
        config = FrameworkConfig()
        config.plugins.plugin_directories = ["plugins"]
        
        plugin_manager = PluginManager(config)
        await plugin_manager.initialize()
        
        model_integration = ModelIntegration(plugin_manager)
        
        # Step 1: AI model discovers available functions
        logger.info("Step 1: Function Discovery")
        functions = model_integration.get_functions_schema(format="openai")
        
        logger.info(f"AI Model sees {len(functions)} available functions:")
        for func in functions[:3]:  # Show first 3
            logger.info(f"  Function: {func['name']}")
            logger.info(f"  Description: {func['description']}")
            logger.info(f"  Parameters: {list(func['parameters']['properties'].keys())}")
        
        # Step 2: AI model searches for specific capabilities
        logger.info("\nStep 2: Capability Search")
        text_functions = model_integration.search_functions("text processing")
        math_functions = model_integration.search_functions("calculation")
        
        logger.info(f"Text processing functions: {[f['name'] for f in text_functions]}")
        logger.info(f"Math functions: {[f['name'] for f in math_functions]}")
        
        # Step 3: AI model executes functions based on user request
        logger.info("\nStep 3: Function Execution")
        
        # Simulate user request: "Convert 'hello world' to uppercase and calculate 15 * 3"
        
        # Execute text processing
        if text_functions:
            text_request = FunctionCallRequest(
                function_name=text_functions[0]['name'],
                arguments={
                    "text": "hello world",
                    "operation": "uppercase"
                }
            )
            text_result = await model_integration.call_function(text_request)
            logger.info(f"Text processing: {text_result.result}")
        
        # Execute calculation
        if math_functions:
            calc_request = FunctionCallRequest(
                function_name=math_functions[0]['name'],
                arguments={
                    "a": 15,
                    "b": 3,
                    "operation": "multiply"
                }
            )
            calc_result = await model_integration.call_function(calc_request)
            logger.info(f"Calculation: {calc_result.result}")
        
        # Step 4: AI model gets execution statistics
        logger.info("\nStep 4: Execution Monitoring")
        stats = model_integration.get_call_statistics()
        logger.info(f"Total function calls: {stats['total_calls']}")
        logger.info(f"Average execution time: {stats['average_execution_time']:.3f}s")
        
        await plugin_manager.shutdown()
        
    except Exception as e:
        logger.error(f"AI model integration demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    async def main():
        """Main test function."""
        print("Enhanced Plugin System Test")
        print("=" * 50)
        
        # Run the main test
        success = await test_enhanced_plugin_system()
        
        if success:
            print("\n" + "=" * 50)
            print("Running AI Model Integration Demo")
            print("=" * 50)
            await demonstrate_ai_model_integration()
        
        print("\nTest completed!")
        return success
    
    # Run the test
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
