# Enhanced Plugin System Documentation

## Overview

The enhanced plugin system provides advanced capabilities for dynamic plugin management, including hot reload functionality, standardized function exposure, and seamless AI model integration. This system allows plugins to be developed, deployed, and updated without restarting the agent framework.

## Key Features

### 1. Hot Reload Capability
- **File System Monitoring**: Automatically detects changes to plugin files
- **Safe Unloading**: Properly cleans up plugin resources before reload
- **Dependency Management**: Handles plugin dependencies during reload
- **Error Recovery**: Graceful handling of failed reload attempts
- **State Persistence**: Plugins can save and restore state across reloads

### 2. Plugin Function Exposure
- **Decorator-Based**: Simple decorators to expose functions to AI models
- **Automatic Discovery**: Functions are automatically discovered and registered
- **Type Safety**: Parameter and return type validation
- **Schema Generation**: Automatic OpenAPI-style schema generation
- **Documentation**: Rich metadata and examples for AI model understanding

### 3. Model Integration
- **Function Discovery**: AI models can discover available plugin functions
- **Schema Formats**: Support for OpenAI, Anthropic, and generic schemas
- **Search Capabilities**: Semantic search for relevant functions
- **Execution Monitoring**: Statistics and performance tracking
- **Error Handling**: Comprehensive error handling and reporting

## Quick Start

### 1. Creating an Enhanced Plugin

```python
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, returns, ParameterType

class MyPlugin(EnhancedPlugin):
    PLUGIN_NAME = "my_plugin"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "My enhanced plugin"
    
    @property
    def name(self) -> str:
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config):
        # Plugin-specific initialization
        pass
    
    async def _cleanup_plugin(self):
        # Plugin-specific cleanup
        pass
    
    async def _execute_capability(self, request):
        # Handle traditional capability requests
        pass
    
    @exposed_function(
        description="Process text with various operations",
        tags=["text", "processing"]
    )
    @string_param("text", "Text to process", required=True)
    @string_param("operation", "Operation to perform", required=True)
    @returns(ParameterType.STRING, "Processed text")
    async def process_text(self, text: str, operation: str) -> str:
        if operation == "uppercase":
            return text.upper()
        elif operation == "lowercase":
            return text.lower()
        else:
            raise ValueError(f"Unknown operation: {operation}")
```

### 2. Configuring Hot Reload

```python
from agent_framework.core.config import FrameworkConfig
from agent_framework.plugins import PluginManager, HotReloadConfig

# Configure hot reload
hot_reload_config = HotReloadConfig(
    enabled=True,
    debounce_delay=1.0,  # Wait 1 second after file change
    max_reload_attempts=3,
    watch_patterns=["*.py"],
    ignore_patterns=["__pycache__", "*.pyc"]
)

# Initialize plugin manager with hot reload
config = FrameworkConfig()
plugin_manager = PluginManager(config, hot_reload_config=hot_reload_config)
await plugin_manager.initialize()
```

### 3. AI Model Integration

```python
from agent_framework.plugins import ModelIntegration, FunctionCallRequest

# Create model integration
model_integration = ModelIntegration(plugin_manager)

# Discover available functions
functions = model_integration.get_available_functions()

# Get OpenAI-compatible schemas
schemas = model_integration.get_functions_schema(format="openai")

# Call a function
request = FunctionCallRequest(
    function_name="my_plugin.process_text",
    arguments={"text": "Hello World", "operation": "uppercase"}
)
result = await model_integration.call_function(request)
```

## Function Decorators

### Basic Decorators

- `@exposed_function()`: Mark a function for AI model access
- `@parameter()`: Define parameter metadata
- `@returns()`: Define return type metadata
- `@example()`: Add usage examples

### Parameter Type Decorators

- `@string_param()`: String parameter
- `@int_param()`: Integer parameter
- `@float_param()`: Float parameter
- `@bool_param()`: Boolean parameter
- `@array_param()`: Array parameter
- `@object_param()`: Object parameter

### Utility Decorators

- `@validate_input()`: Custom input validation
- `@validate_output()`: Custom output validation
- `@cached()`: Cache function results
- `@rate_limited()`: Rate limit function calls

## Hot Reload Lifecycle

### Plugin Lifecycle Events

1. **Before Reload**: `on_before_reload()` - Save state
2. **Unload**: Plugin cleanup and resource deallocation
3. **Module Cache Clear**: Remove from Python module cache
4. **Reload**: Load updated plugin code
5. **Initialize**: Plugin initialization with new code
6. **After Reload**: `on_after_reload()` - Restore state

### State Persistence

```python
class MyPlugin(EnhancedPlugin):
    async def _save_reload_state(self):
        # Save state before reload
        state = {"counter": self.counter, "data": self.data}
        with open("plugin_state.json", "w") as f:
            json.dump(state, f)
    
    async def _restore_reload_state(self):
        # Restore state after reload
        try:
            with open("plugin_state.json", "r") as f:
                state = json.load(f)
            self.counter = state.get("counter", 0)
            self.data = state.get("data", {})
        except FileNotFoundError:
            pass
```

## Configuration Options

### Plugin Configuration

```python
class PluginConfig(BaseModel):
    # Basic settings
    plugin_directories: List[str] = ["plugins"]
    auto_load_plugins: bool = True
    
    # Hot reload settings
    hot_reload_enabled: bool = True
    hot_reload_debounce_delay: float = 1.0
    hot_reload_max_attempts: int = 3
    hot_reload_timeout: float = 30.0
    hot_reload_watch_patterns: List[str] = ["*.py"]
    hot_reload_ignore_patterns: List[str] = ["__pycache__", "*.pyc"]
    
    # Function exposure settings
    function_exposure_enabled: bool = True
    function_validation_enabled: bool = True
    function_cache_ttl: int = 300
```

## Error Handling

### Hot Reload Errors

- **File System Errors**: Monitoring failures, permission issues
- **Module Loading Errors**: Syntax errors, import failures
- **Dependency Errors**: Missing dependencies, circular dependencies
- **Initialization Errors**: Plugin initialization failures

### Function Call Errors

- **Validation Errors**: Parameter type mismatches, missing required parameters
- **Execution Errors**: Runtime exceptions in plugin functions
- **Timeout Errors**: Function execution timeouts
- **Permission Errors**: Security sandbox violations

### Error Recovery

- **Rollback**: Restore previous plugin version on reload failure
- **Graceful Degradation**: Continue operation with reduced functionality
- **Retry Logic**: Automatic retry with exponential backoff
- **Logging**: Comprehensive error logging and monitoring

## Best Practices

### Plugin Development

1. **Use Type Hints**: Provide clear type annotations for all parameters
2. **Add Documentation**: Include comprehensive docstrings and examples
3. **Handle Errors**: Implement proper error handling and validation
4. **State Management**: Design for hot reload with proper state persistence
5. **Resource Cleanup**: Always clean up resources in cleanup methods

### Function Exposure

1. **Clear Naming**: Use descriptive function and parameter names
2. **Rich Metadata**: Provide detailed descriptions and examples
3. **Validation**: Implement input and output validation
4. **Error Messages**: Provide clear, actionable error messages
5. **Performance**: Consider caching and rate limiting for expensive operations

### Hot Reload

1. **Stateless Design**: Minimize state that needs to persist across reloads
2. **Graceful Shutdown**: Implement proper cleanup in lifecycle methods
3. **Dependency Management**: Be aware of plugin dependencies
4. **Testing**: Test reload scenarios thoroughly
5. **Monitoring**: Monitor reload events and failures

## Monitoring and Debugging

### Statistics and Metrics

- Function call counts and success rates
- Average execution times
- Plugin reload events and failures
- Memory usage and performance metrics

### Logging

- Structured logging with correlation IDs
- Different log levels for different components
- Plugin-specific log namespaces
- Hot reload event logging

### Debugging Tools

- Plugin status and health checks
- Function registry inspection
- Hot reload history and diagnostics
- Performance profiling and analysis

## Migration Guide

### From Basic Plugins

1. **Inherit from EnhancedPlugin**: Replace PluginInterface with EnhancedPlugin
2. **Add Function Decorators**: Mark functions with @exposed_function
3. **Implement Lifecycle Methods**: Add _initialize_plugin and _cleanup_plugin
4. **Add State Management**: Implement reload state persistence if needed

### Configuration Updates

1. **Update Config**: Add hot reload configuration options
2. **Enable Features**: Set hot_reload_enabled and function_exposure_enabled
3. **Adjust Timeouts**: Configure appropriate timeouts for your use case
4. **Set Watch Patterns**: Configure file patterns to monitor

## Troubleshooting

### Common Issues

1. **Hot Reload Not Working**: Check file permissions and watch patterns
2. **Functions Not Discovered**: Verify @exposed_function decorators
3. **Validation Errors**: Check parameter types and schemas
4. **Performance Issues**: Review caching and rate limiting settings
5. **Memory Leaks**: Ensure proper cleanup in lifecycle methods

### Debug Commands

```python
# Check plugin status
stats = plugin_manager.get_plugin_stats()

# View available functions
functions = plugin_manager.get_available_functions()

# Check reload history
history = plugin_manager.get_reload_history()

# View function call statistics
call_stats = model_integration.get_call_statistics()
```
